// 全局变量
let currentUserId = null;
let currentUserName = null;
let currentUserPosition = null;
let currentMessages = [];
let isWaitingForResponse = false;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM content loaded');
    
    // 加载用户列表
    loadUsers();
    
    // 加载保存的APIKey
    loadSavedApiKey();
    
    // 添加事件监听器
    document.getElementById('sendMessageBtn').addEventListener('click', sendMessage);
    document.getElementById('clearChatBtn').addEventListener('click', clearChat);
    
    // 确保查看提示词按钮有事件监听器
    const viewPromptBtn = document.getElementById('viewPromptBtn');
    if (viewPromptBtn) {
        console.log('Adding event listener to viewPromptBtn');
        viewPromptBtn.addEventListener('click', showUserPrompt);
    } else {
        console.error('viewPromptBtn not found in DOM');
    }
    
    // 为APIKey输入框添加事件监听器，保存到localStorage
    const apiKeyInput = document.getElementById('apiKeyInput');
    if (apiKeyInput) {
        apiKeyInput.addEventListener('change', saveApiKey);
    }
    
    document.getElementById('messageInput').addEventListener('keydown', (e) => {
        // 按下 Enter 发送消息，Shift+Enter 换行
        if (e.key === 'Enter') {
            if (e.shiftKey) {
                // Shift+Enter 正常输入回车，不做处理
                return;
            } else {
                // 单独按 Enter 发送消息
                e.preventDefault();
                sendMessage();
            }
        }
    });
    
    // 搜索框功能
    document.getElementById('userSearch').addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        filterUsers(searchTerm);
    });
    
    // 模态框关闭按钮
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', () => {
            const modal = button.closest('.modal');
            if (modal) {
                modal.classList.remove('show');
                // 确保移除内联样式
                modal.style.display = '';
            }
        });
    });
    
    // 点击模态框背景关闭 - 已禁用，只允许通过关闭按钮关闭
    /*
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
                // 确保移除内联样式
                modal.style.display = '';
            }
        });
    });
    */
});

// 加载租户和职位分类结构
async function loadUsers() {
    try {
        const token = localStorage.getItem('accessToken');

        // 获取所有职位分类
        const response = await fetch('/web_job_classifications', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();

        if (result.code === 200) {
            renderJobClassificationsList(result.data);
        } else {
            throw new Error(result.message || '获取职位分类失败');
        }
    } catch (error) {
        console.error('加载职位分类失败:', error);
        showNotification('加载职位分类失败，请刷新页面重试。', 'error');
    }
}

// 渲染职位分类列表
function renderJobClassificationsList(jobClassifications) {
    const usersList = document.getElementById('usersList');

    if (jobClassifications.length === 0) {
        usersList.innerHTML = '<div class="no-users-message">没有找到职位分类数据</div>';
        return;
    }

    // 清空加载中的提示
    usersList.innerHTML = '';

    // 按租户分组职位分类
    const groupedClassifications = {};
    const tenantNames = {};

    jobClassifications.forEach(classification => {
        const tenantId = classification.tenant_id || 'unknown_tenant';

        if (!groupedClassifications[tenantId]) {
            groupedClassifications[tenantId] = [];
        }

        groupedClassifications[tenantId].push(classification);
    });

    // 获取租户名称
    const token = localStorage.getItem('accessToken');
    const tenantIds = Object.keys(groupedClassifications).filter(id => id !== 'unknown_tenant');

    const fetchTenantNames = async () => {
        try {
            for (const tenantId of tenantIds) {
                const response = await fetch(`/api/tenant/${tenantId}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();
                if (result.code === 200 && result.data) {
                    tenantNames[tenantId] = `${result.data.nickname}:${result.data.account}`;
                } else {
                    tenantNames[tenantId] = `租户 ${tenantId.substring(0, 8)}`;
                }
            }
        } catch (error) {
            console.error('加载租户信息失败:', error);
            tenantIds.forEach(id => {
                tenantNames[id] = `租户 ${id.substring(0, 8)}`;
            });
        } finally {
            renderGroups();
        }
    };

    const renderGroups = () => {
        Object.keys(groupedClassifications).forEach(tenantId => {
            const tenantGroup = document.createElement('div');
            tenantGroup.className = 'tenant-group collapsed'; // 默认折叠

            // 创建租户标题
            const tenantHeader = document.createElement('div');
            tenantHeader.className = 'group-header tenant-header';
            const tenantName = tenantNames[tenantId] || `租户 ${tenantId.substring(0, 8)}`;

            tenantHeader.innerHTML = `
                <i class="fas fa-building"></i>
                <span>${tenantName}</span>
                <i class="fas fa-chevron-right toggle-icon"></i>
            `;

            // 租户点击事件 - 展开/折叠职位分类
            tenantHeader.addEventListener('click', async () => {
                const isCollapsed = tenantGroup.classList.contains('collapsed');
                tenantGroup.classList.toggle('collapsed');
                const icon = tenantHeader.querySelector('.toggle-icon');
                icon.classList.toggle('fa-chevron-down');
                icon.classList.toggle('fa-chevron-right');

                // 如果是展开操作，加载职位分类
                if (isCollapsed) {
                    await loadJobClassificationsForTenant(tenantId, tenantContent);
                }
            });

            tenantGroup.appendChild(tenantHeader);

            // 创建租户内容容器
            const tenantContent = document.createElement('div');
            tenantContent.className = 'tenant-content';
            tenantGroup.appendChild(tenantContent);

            usersList.appendChild(tenantGroup);
        });
    };

    fetchTenantNames();
}

// 加载租户下的职位分类
async function loadJobClassificationsForTenant(tenantId, tenantContent) {
    try {
        const token = localStorage.getItem('accessToken');
        const response = await fetch('/web_job_classifications', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();

        if (result.code === 200) {
            // 筛选出该租户的职位分类
            const tenantClassifications = result.data.filter(classification =>
                classification.tenant_id === tenantId
            );

            // 清空内容
            tenantContent.innerHTML = '';

            // 渲染职位分类
            tenantClassifications.forEach(classification => {
                const classificationGroup = document.createElement('div');
                classificationGroup.className = 'classification-group collapsed'; // 默认折叠

                // 创建职位分类标题
                const classificationHeader = document.createElement('div');
                classificationHeader.className = 'group-header classification-header';

                classificationHeader.innerHTML = `
                    <i class="fas fa-tags"></i>
                    <span>${classification.class_name || '未命名分类'}</span>
                    <span class="classification-count">(<span class="user-count">-</span>)</span>
                    <i class="fas fa-chevron-right toggle-icon"></i>
                `;

                // 职位分类点击事件 - 展开/折叠用户列表
                classificationHeader.addEventListener('click', async () => {
                    const isCollapsed = classificationGroup.classList.contains('collapsed');
                    classificationGroup.classList.toggle('collapsed');
                    const icon = classificationHeader.querySelector('.toggle-icon');
                    icon.classList.toggle('fa-chevron-down');
                    icon.classList.toggle('fa-chevron-right');

                    // 如果是展开操作，加载用户列表
                    if (isCollapsed) {
                        await loadUsersForJobClassification(classification.id, classificationContent, classificationHeader);
                    }
                });

                classificationGroup.appendChild(classificationHeader);

                // 创建职位分类内容容器
                const classificationContent = document.createElement('div');
                classificationContent.className = 'classification-content';
                classificationGroup.appendChild(classificationContent);

                tenantContent.appendChild(classificationGroup);
            });
        }
    } catch (error) {
        console.error('加载职位分类失败:', error);
        tenantContent.innerHTML = '<div class="error-message">加载职位分类失败</div>';
    }
}

// 加载职位分类下的用户
async function loadUsersForJobClassification(classificationId, classificationContent, classificationHeader) {
    try {
        const token = localStorage.getItem('accessToken');

        // 显示加载状态
        classificationContent.innerHTML = '<div class="loading-message">加载用户中...</div>';

        const response = await fetch(`/job_classifications/${classificationId}/users`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();

        if (result.code === 200) {
            const users = result.data;

            // 更新用户数量
            const countSpan = classificationHeader.querySelector('.user-count');
            if (countSpan) {
                countSpan.textContent = users.length;
            }

            // 清空内容
            classificationContent.innerHTML = '';

            if (users.length === 0) {
                classificationContent.innerHTML = '<div class="no-users-message">该职位分类下暂无用户</div>';
                return;
            }

            // 渲染用户列表
            users.forEach(user => {
                const userItem = document.createElement('div');
                userItem.className = 'user-item';
                userItem.dataset.userId = user.userid;

                if (currentUserId === user.userid) {
                    userItem.classList.add('active');
                }

                // 生成随机颜色作为头像背景
                const avatarColor = getRandomColor(user.userid);
                const initials = (user.name || 'U').charAt(0).toUpperCase();

                userItem.innerHTML = `
                    <div class="user-avatar" style="background-color: ${avatarColor}; color: white; display: flex; justify-content: center; align-items: center;">
                        ${initials}
                    </div>
                    <div class="user-info">
                        <h4 class="user-name">${user.name || '匿名用户'}</h4>
                    </div>
                `;

                userItem.addEventListener('click', () => selectUser(user));
                classificationContent.appendChild(userItem);
            });
        } else {
            throw new Error(result.message || '获取用户列表失败');
        }
    } catch (error) {
        console.error('加载用户列表失败:', error);
        classificationContent.innerHTML = '<div class="error-message">加载用户失败</div>';
    }
}

// 筛选用户
function filterUsers(searchTerm) {
    const tenantGroups = document.querySelectorAll('.tenant-group');

    tenantGroups.forEach(tenantGroup => {
        let tenantVisible = false;
        const tenantHeader = tenantGroup.querySelector('.tenant-header span').textContent.toLowerCase();

        // 如果租户名称匹配，显示整个租户组
        if (tenantHeader.includes(searchTerm)) {
            tenantVisible = true;
            tenantGroup.style.display = '';

            // 显示所有职位分类组和用户
            tenantGroup.querySelectorAll('.classification-group').forEach(classGroup => {
                classGroup.style.display = '';
                classGroup.querySelectorAll('.user-item').forEach(item => {
                    item.style.display = '';
                });
            });
        } else {
            // 检查职位分类组
            const classificationGroups = tenantGroup.querySelectorAll('.classification-group');
            let anyClassificationVisible = false;

            classificationGroups.forEach(classGroup => {
                const classificationHeader = classGroup.querySelector('.classification-header span').textContent.toLowerCase();
                let classificationVisible = classificationHeader.includes(searchTerm);

                // 如果职位分类名称匹配，显示该分类组
                if (classificationVisible) {
                    classGroup.style.display = '';
                    anyClassificationVisible = true;

                    // 显示所有用户
                    classGroup.querySelectorAll('.user-item').forEach(item => {
                        item.style.display = '';
                    });
                } else {
                    // 检查用户
                    const userItems = classGroup.querySelectorAll('.user-item');
                    let anyUserVisible = false;

                    userItems.forEach(item => {
                        const userName = item.querySelector('.user-name').textContent.toLowerCase();
                        if (userName.includes(searchTerm)) {
                            item.style.display = '';
                            anyUserVisible = true;
                            classificationVisible = true;
                            anyClassificationVisible = true;
                        } else {
                            item.style.display = 'none';
                        }
                    });

                    classGroup.style.display = anyUserVisible ? '' : 'none';
                }
            });

            tenantVisible = anyClassificationVisible;
            tenantGroup.style.display = tenantVisible ? '' : 'none';
        }
    });
}

// 选择用户
function selectUser(user) {
    // 如果当前已经选择了这个用户，则不做操作
    if (currentUserId === user.userid) return;
    
    currentUserId = user.userid;
    currentUserName = user.name || '匿名用户';
    currentMessages = [];
    
    // 更新UI
    document.querySelectorAll('.user-item').forEach(item => {
        item.classList.toggle('active', item.dataset.userId === user.userid);
    });
    
    // 更新聊天窗口信息
    document.getElementById('chatUserName').textContent = currentUserName;
    
    // 获取职位信息
    if (user.positionId) {
        const token = localStorage.getItem('accessToken');
        fetch(`/web_position/${user.positionId}`,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(position => {
                if (position && position.positionName) {
                    currentUserPosition = position.positionName;
                    document.getElementById('chatUserPosition').textContent = currentUserPosition;
                }
            })
            .catch(error => {
                console.error('加载职位信息失败:', error);
                document.getElementById('chatUserPosition').textContent = '未知职位';
            });
    } else {
        document.getElementById('chatUserPosition').textContent = '未分配职位';
    }
    
    // 设置头像
    const avatarColor = getRandomColor(user.userid);
    const initials = (user.name || 'U').charAt(0).toUpperCase();
    document.getElementById('chatUserAvatar').outerHTML = `
        <div id="chatUserAvatar" class="chat-avatar" style="background-color: ${avatarColor}; color: white; display: flex; justify-content: center; align-items: center;">
            ${initials}
        </div>
    `;
    
    // 显示用户分数
    document.getElementById('userScore').textContent = user.score || 0;
    
    // 获取并显示用户当前场景
    if (user.sceneId) {
        const token = localStorage.getItem('accessToken');
        fetch(`/scene/${user.sceneId}`,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(scene => {
                if (scene && scene.sceneName) {
                    document.getElementById('userScene').textContent = scene.sceneName;
                    document.getElementById('userScene').setAttribute('data-scene-id', user.sceneId);
                } else {
                    document.getElementById('userScene').textContent = '默认场景';
                    document.getElementById('userScene').setAttribute('data-scene-id', '');
                }
            })
            .catch(error => {
                console.error('加载场景信息失败:', error);
                document.getElementById('userScene').textContent = '未知场景';
                document.getElementById('userScene').setAttribute('data-scene-id', '');
            });
    } else {
        document.getElementById('userScene').textContent = '未设置场景';
        document.getElementById('userScene').setAttribute('data-scene-id', '');
    }
    
    // 启用输入框和按钮
    document.getElementById('messageInput').disabled = false;
    document.getElementById('sendMessageBtn').disabled = false;
    document.getElementById('clearChatBtn').disabled = false;
    document.getElementById('viewPromptBtn').disabled = false;
    
    // 清空聊天窗口并显示欢迎消息
    const chatWindow = document.getElementById('chatWindow');
    chatWindow.innerHTML = '';
    
    
    // 加载历史消息
    loadChatHistory(user.userid);
}

// 加载聊天历史
async function loadChatHistory(userId) {
    try {
        console.log('Loading chat history for user:', userId);
        
        // 显示加载中状态
        const chatWindow = document.getElementById('chatWindow');
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message-container';
        loadingDiv.innerHTML = '<div class="message message-system">加载聊天历史中...</div>';
        chatWindow.appendChild(loadingDiv);
        
        // 调用API获取聊天历史
        const token = localStorage.getItem('accessToken');
        const response = await fetch(`/chat/history/${userId}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const chatLogs = await response.json();
        
        // 移除加载消息
        if (loadingDiv && loadingDiv.parentNode) {
            loadingDiv.parentNode.removeChild(loadingDiv);
        }
        
        
        // 处理并显示聊天记录
        const messages = [];
        let scoreTrackingCount = 0;
        
        chatLogs.forEach(log => {
            const message = {
                role: log.role,
                content: log.content || '',  // 确保content存在，防止null或undefined
                timestamp: log.createdAt  // 直接使用createdAt作为时间戳
            };
            
            // 添加分数跟踪数据（如果存在）
            if (log.role === 'assistant' && log.score_tracking) {
                message.scoreTracking = log.score_tracking;
                scoreTrackingCount++;
            }
            
            // 添加actions数据（如果存在）
            if (log.role === 'assistant' && log.actions) {
                message.actions = log.actions;
            }
            
            // 添加channel数据（如果存在）
            if (log.channel) {
                message.channel = log.channel;
            }
            
            messages.push(message);
            addMessageToChat(message);
        });
        
        // 更新当前消息数组
        currentMessages = messages;
        
    } catch (error) {
        console.error('加载聊天历史失败:', error);
        showNotification('加载聊天历史失败，请刷新页面重试。', 'error');
    }
}

// 发送消息
async function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message || !currentUserId || isWaitingForResponse) return;
    
    // 清空输入框
    messageInput.value = '';
    
    // 创建消息对象
    const userMessage = {
        role: 'user',
        content: message,
        timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 添加消息到聊天窗口
    addMessageToChat(userMessage);
    
    // 添加到消息列表
    currentMessages.push(userMessage);
    
    // 显示正在输入状态
    showTypingIndicator();
    isWaitingForResponse = true;
    
    try {
        // 获取APIKey
        const apiKeyInput = document.getElementById('apiKeyInput');
        const apiKey = apiKeyInput ? apiKeyInput.value.trim() : '';
        
        // 准备请求头
        const headers = {
            'Content-Type': 'application/json'
        };
        
        // 如果有APIKey，添加到Authorization头
        if (apiKey) {
            headers['Authorization'] = `Bearer ${apiKey}`;
        }
        
        // 发送请求
        const response = await fetch('/chat', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify({
                messages: currentMessages,
                name: currentUserName,
                userid: currentUserId,
                is_reply: true
            })
        });
        
        const result = await response.json();
        
        // 隐藏输入状态
        hideTypingIndicator();
        isWaitingForResponse = false;
        
        if (result.code === 200) {
            // 创建系统回复消息
            const systemMessage = {
                role: 'assistant',
                content: result.content,
                timestamp: Math.floor(Date.now() / 1000)
            };
            
            // 添加分数跟踪信息（如果存在）
            if (result.score_tracking) {
                systemMessage.scoreTracking = result.score_tracking;
            }
            
            // 添加actions信息（如果存在）- 更健壮的检测方式
            if (result.actions && Array.isArray(result.actions) && result.actions.length > 0) {
                systemMessage.actions = result.actions;
            } else if (result.content && typeof result.content === 'object' && result.content.actions) {
                // 如果actions嵌套在content中
                systemMessage.actions = result.content.actions;
            }
            
            // 添加channel信息（如果存在）
            if (result.channel) {
                systemMessage.channel = result.channel;
            }
            
            // 添加消息到聊天窗口
            addMessageToChat(systemMessage);
            
            // 添加到消息列表
            currentMessages.push(systemMessage);

            // 更新场景
            if (result.sence) {
                document.getElementById('userScene').textContent = result.sence.sceneName;
                document.getElementById('userScene').setAttribute('data-scene-id', result.sence.sceneId);
            }
            
            // 更新分数
            document.getElementById('userScore').textContent = result.score || 0;
            
            // 更新场景（如果场景发生变化）
            if (result.sceneId && result.sceneId !== document.getElementById('userScene').getAttribute('data-scene-id')) {
                const token = localStorage.getItem('accessToken');
                fetch(`/scene/${result.sceneId}`,{
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                })
                    .then(response => response.json())
                    .then(scene => {
                        if (scene && scene.sceneName) {
                            document.getElementById('userScene').textContent = scene.sceneName;
                            document.getElementById('userScene').setAttribute('data-scene-id', result.sceneId);
                        }
                    })
                    .catch(error => {
                        console.error('加载场景信息失败:', error);
                    });
            }
        } else {
            throw new Error(result.message || '发送消息失败');
        }
    } catch (error) {
        console.error('发送消息失败:', error);
        hideTypingIndicator();
        isWaitingForResponse = false;
        
        // 显示错误消息
        const errorMessage = {
            role: 'system',
            content: error.message || '消息发送失败，请重试。',
            timestamp: Math.floor(Date.now() / 1000),
            isError: true
        };
        
        addMessageToChat(errorMessage);
        
        // 使用通知确保用户看到错误
        showNotification(error.message || '消息发送失败，请重试。', 'error');
    }
}

// 添加消息到聊天窗口
function addMessageToChat(message) {
    const chatWindow = document.getElementById('chatWindow');
    const messageContainer = document.createElement('div');
    messageContainer.className = 'message-container';
    
    // 根据角色创建不同样式的消息
    const messageElement = document.createElement('div');
    
    if (message.role === 'user') {
        messageElement.className = 'message message-user';
    } else if (message.role === 'assistant') {
        messageElement.className = 'message message-system';
    } else {
        // 系统消息
        messageElement.className = 'message message-system';
        if (message.isError) {
            messageElement.style.backgroundColor = '#ffebee';
            messageElement.style.color = '#c62828';
        }
    }
    
    // 添加channel标签（如果存在）
    if (message.channel) {
        messageElement.classList.add('has-channel');
        
        // 创建channel标签元素
        const channelTag = document.createElement('div');
        channelTag.className = 'channel-tag';
        channelTag.textContent = message.channel;
        channelTag.setAttribute('data-channel', message.channel.toLowerCase());
        messageElement.appendChild(channelTag);
    }
    
    // 消息内容
    if (message.content) {
        // 创建内容容器来保证内容和channel标签不冲突
        const contentElement = document.createElement('div');
        contentElement.className = 'message-content';
        contentElement.innerHTML = message.content.toString().replace(/\n/g, '<br>');
        messageElement.appendChild(contentElement);
    } else {
        // 创建空的内容容器
        const contentElement = document.createElement('div');
        contentElement.className = 'message-content';
        contentElement.innerHTML = ''; // 如果没有内容则显示空
        messageElement.appendChild(contentElement);
    }
    
    // 添加时间戳
    if (message.timestamp) {
        const metaElement = document.createElement('div');
        metaElement.className = 'message-meta';
        
        const timeElement = document.createElement('span');
        timeElement.className = 'message-time';
        timeElement.textContent = formatTimestamp(message.timestamp);
        
        metaElement.appendChild(timeElement);
        
        // 如果是AI消息且有分数跟踪数据，添加分数按钮
        if (message.role === 'assistant' && message.scoreTracking) {
            const scoreButton = document.createElement('button');
            scoreButton.className = 'score-tracking-btn';
            scoreButton.innerHTML = '<i class="fas fa-chart-line"></i>';
            scoreButton.title = '查看分数变化';
            
            // 检查分数变化并添加相应的类
            if (message.scoreTracking.current_score > message.scoreTracking.original_score) {
                scoreButton.classList.add('score-increase');
            } else if (message.scoreTracking.current_score < message.scoreTracking.original_score) {
                scoreButton.classList.add('score-decrease');
            }
            
            // 如果是从历史记录加载的消息（判断timestamp早于当前时间超过10秒）
            const now = Math.floor(Date.now() / 1000);
            if (message.timestamp && now - message.timestamp > 10) {
                // 给历史消息添加一个淡入效果
                setTimeout(() => {
                    scoreButton.classList.add('highlight-history');
                    // 2秒后移除高亮效果
                    setTimeout(() => {
                        scoreButton.classList.remove('highlight-history');
                    }, 2000);
                }, 500);
            }
            
            // 使用内联函数直接添加事件
            const clickHandler = function(event) {
                event.preventDefault();
                event.stopPropagation();
                showScoreTrackingPopup(message.scoreTracking, scoreButton);
            };
            
            // 同时添加click和touchend事件，提高移动设备的响应性
            scoreButton.addEventListener('click', clickHandler);
            scoreButton.addEventListener('touchend', clickHandler);
            
            // 添加明确的视觉反馈
            scoreButton.addEventListener('mousedown', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            scoreButton.addEventListener('mouseup', function() {
                this.style.transform = '';
            });
            
            metaElement.appendChild(scoreButton);
        }
        
        // 如果是AI消息且有actions字段，添加查看actions按钮
        if (message.role === 'assistant') {
            // 始终为AI消息添加actions按钮
            const actionsButton = document.createElement('button');
            actionsButton.className = 'actions-btn';
            
            // 检查是否有actions数据，如果有则添加高亮样式
            if (message.actions && message.actions.length > 0) {
                actionsButton.classList.add('has-actions');
                actionsButton.innerHTML = '<i class="fas fa-code has-data"></i>';
            } else {
            actionsButton.innerHTML = '<i class="fas fa-code"></i>';
            }
            
            actionsButton.title = '查看Actions';
            
            // 使用内联函数直接添加事件
            const actionsClickHandler = function(event) {
                event.preventDefault();
                event.stopPropagation();
                showActionsPopup(message.actions || [], actionsButton);
            };
            
            // 同时添加click和touchend事件，提高移动设备的响应性
            actionsButton.addEventListener('click', actionsClickHandler);
            actionsButton.addEventListener('touchend', actionsClickHandler);
            
            // 添加明确的视觉反馈
            actionsButton.addEventListener('mousedown', function() {
                this.style.transform = 'scale(0.95)';
            });
            
            actionsButton.addEventListener('mouseup', function() {
                this.style.transform = '';
            });
            
            metaElement.appendChild(actionsButton);
        }
        
        messageElement.appendChild(metaElement);
    }
    
    // 添加到容器
    messageContainer.appendChild(messageElement);
    chatWindow.appendChild(messageContainer);
    
    // 滚动到底部
    chatWindow.scrollTop = chatWindow.scrollHeight;
}

// 显示正在输入指示器
function showTypingIndicator() {
    const chatWindow = document.getElementById('chatWindow');
    
    // 如果已经有输入指示器，则不重复添加
    if (document.querySelector('.message-loading')) return;
    
    const loadingContainer = document.createElement('div');
    loadingContainer.className = 'message-container';
    
    const loadingElement = document.createElement('div');
    loadingElement.className = 'message message-system message-loading';
    loadingElement.innerHTML = `
        <div class="dot"></div>
        <div class="dot"></div>
        <div class="dot"></div>
    `;
    
    loadingContainer.appendChild(loadingElement);
    chatWindow.appendChild(loadingContainer);
    
    // 滚动到底部
    chatWindow.scrollTop = chatWindow.scrollHeight;
}

// 隐藏正在输入指示器
function hideTypingIndicator() {
    const loadingElement = document.querySelector('.message-loading');
    if (loadingElement) {
        loadingElement.parentElement.remove();
    }
}

// 清空聊天
async function clearChat() {
    if (!currentUserId) return;
    
    if (confirm('确定要清空聊天记录吗？')) {
        try {
            // 调用API删除聊天记录
            const token = localStorage.getItem('accessToken');
            const response = await fetch(`/chat/history/${currentUserId}`, {
                method: 'DELETE',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                // 清空聊天窗口
                document.getElementById('chatWindow').innerHTML = '';
                
                // 清空消息列表
                currentMessages = [];
                
                showNotification('聊天记录已清空', 'success');
            } else {
                throw new Error(result.message || '清空聊天记录失败');
            }
        } catch (error) {
            console.error('清空聊天记录失败:', error);
            showNotification(error.message || '清空聊天记录失败，请重试。', 'error');
            
            // 添加错误消息到聊天窗口
            const errorMessage = {
                role: 'system',
                content: error.message || '清空聊天记录失败，请重试。',
                timestamp: Math.floor(Date.now() / 1000),
                isError: true
            };
            
            addMessageToChat(errorMessage);
        }
    }
}

// 根据用户ID生成随机但固定的颜色
function getRandomColor(userId) {
    // 使用用户ID作为种子生成固定的颜色
    const colors = [
        '#1abc9c', '#2ecc71', '#3498db', '#9b59b6', '#34495e',
        '#16a085', '#27ae60', '#2980b9', '#8e44ad', '#2c3e50',
        '#f1c40f', '#e67e22', '#e74c3c', '#95a5a6', '#f39c12',
        '#d35400', '#c0392b', '#7f8c8d'
    ];
    
    // 简单哈希函数生成固定索引
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
        hash = ((hash << 5) - hash) + userId.charCodeAt(i);
        hash |= 0; // 转换为32位整数
    }
    
    const index = Math.abs(hash) % colors.length;
    return colors[index];
}

// 格式化时间戳
function formatTimestamp(timestamp) {
    const date = new Date(timestamp * 1000);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
}

// 显示错误消息
function showError(message) {
    alert(message);
}

// 显示成功消息
function showSuccess(message) {
    alert(message);
}

// 显示用户的当前提示词
async function showUserPrompt() {
    if (!currentUserId) {
        showNotification('请先选择一个候选人', 'error');
        return;
    }
    
    try {
        // 显示加载中状态
        const promptTextEditor = document.getElementById('promptTextEditor');
        const promptPreview = document.getElementById('promptPreview');
        
        promptTextEditor.value = '加载中...';
        promptPreview.innerHTML = '<p>加载中...</p>';
        
        // 显示模态框
        const promptModal = document.getElementById('promptModal');
        promptModal.classList.add('show');
        // 确保模态框可见
        promptModal.style.display = 'block';
        
        // 调用API获取用户的提示词
        const url = `/user/current_prompt/${currentUserId}`;
        const token = localStorage.getItem('accessToken');
        const response = await fetch(url,{
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        const data = await response.json();
        
        if (data.code === 200 && data.prompt) {
            // 显示提示词
            promptTextEditor.value = data.prompt;
            
            // 渲染Markdown预览
            renderPromptPreview();
            
            // 同步两个元素的高度
            syncHeights();
        } else {
            // 显示错误信息
            promptTextEditor.value = '获取提示词失败：' + (data.message || '未知错误');
            promptPreview.innerHTML = '<p class="error">获取提示词失败</p>';
        }
    } catch (error) {
        console.error('获取提示词失败:', error);
        const promptTextEditor = document.getElementById('promptTextEditor');
        const promptPreview = document.getElementById('promptPreview');
        
        promptTextEditor.value = '获取提示词失败：' + error.message;
        promptPreview.innerHTML = '<p class="error">获取提示词失败</p>';
    }
}

// 同步promptTextEditor和promptPreview的高度
function syncHeights() {
    const promptTextEditor = document.getElementById('promptTextEditor');
    const promptPreview = document.getElementById('promptPreview');
    
    // 监听窗口大小变化，重新同步高度
    window.addEventListener('resize', function() {
        updateEditorHeight();
    });
    
    // 立即同步一次高度
    updateEditorHeight();
    
    // 当预览内容变化时，可能高度也会变化，再次同步高度
    // 使用MutationObserver监听DOM变化
    const observer = new MutationObserver(function() {
        updateEditorHeight();
    });
    
    observer.observe(promptPreview, { 
        childList: true,
        subtree: true,
        characterData: true 
    });
    
    function updateEditorHeight() {
        // 获取预览区域的实际高度
        const previewHeight = promptPreview.scrollHeight;
        
        // 设置编辑器的高度
        promptTextEditor.style.height = `${previewHeight}px`;
    }
}

// 渲染提示词预览
function renderPromptPreview() {
    const promptText = document.getElementById('promptTextEditor').value;
    const promptPreview = document.getElementById('promptPreview');
    
    // 检查 marked 库是否可用
    if (typeof marked === 'undefined') {
        console.error('marked library is not loaded');
        promptPreview.innerHTML = '<p class="error">Markdown渲染库未加载，无法显示预览</p>';
        return;
    }
    
    // 使用marked.js渲染Markdown
    try {
        promptPreview.innerHTML = marked.parse(promptText);
    } catch (error) {
        console.error('Error parsing markdown:', error);
        promptPreview.innerHTML = '<p class="error">Markdown解析错误: ' + error.message + '</p>';
    }
}

// 全局通知
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(notification => {
        notification.remove();
    });
    
    // 创建新通知
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 错误消息显示时间更长
    const displayTime = type === 'error' ? 5000 : 2000;
    
    // 自动移除通知
    setTimeout(() => {
        notification.classList.add('notification-hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, displayTime);
}

// 显示分数跟踪弹窗
function showScoreTrackingPopup(scoreTracking, buttonElement) {
    // 检查是否是有效的scoreTracking对象
    if (!scoreTracking || typeof scoreTracking !== 'object') {
        showNotification('无法显示分数数据，数据格式无效', 'error');
        return;
    }
    
    // 检查是否已存在弹窗，如果存在则移除
    const existingPopup = document.querySelector('.score-tracking-popup');
    if (existingPopup) {
        existingPopup.remove();
    }
    
    // 创建弹窗
    const popup = document.createElement('div');
    popup.className = 'score-tracking-popup';
    
    // 创建关闭按钮
    const closeButton = document.createElement('button');
    closeButton.className = 'score-tracking-close';
    closeButton.innerHTML = '&times;';
    closeButton.addEventListener('click', function() {
        popup.remove();
    });
    
    // 创建标题
    const title = document.createElement('h4');
    title.textContent = '分数变化详情';
    
    // 创建内容
    const content = document.createElement('div');
    content.className = 'score-tracking-content';
    
    // 原始分数
    const originalScore = document.createElement('div');
    originalScore.className = 'score-item';
    originalScore.innerHTML = `<span>原始分数:</span> <strong>${scoreTracking.original_score}</strong>`;
    
    // 当前分数
    const currentScore = document.createElement('div');
    currentScore.className = 'score-item';
    currentScore.innerHTML = `<span>当前分数:</span> <strong>${scoreTracking.current_score}</strong>`;
    
    // 分数变化
    const scoreDiff = scoreTracking.current_score - scoreTracking.original_score;
    const scoreChange = document.createElement('div');
    scoreChange.className = 'score-item score-change';
    
    if (scoreDiff > 0) {
        scoreChange.innerHTML = `<span>变化:</span> <strong class="score-positive">+${scoreDiff}</strong>`;
    } else if (scoreDiff < 0) {
        scoreChange.innerHTML = `<span>变化:</span> <strong class="score-negative">${scoreDiff}</strong>`;
    } else {
        scoreChange.innerHTML = `<span>变化:</span> <strong>0</strong>`;
    }
    
    // 变化原因
    const reasonsTitle = document.createElement('div');
    reasonsTitle.className = 'score-reasons-title';
    reasonsTitle.textContent = '变化原因:';
    
    const reasonsList = document.createElement('ul');
    reasonsList.className = 'score-reasons-list';
    
    // 添加所有原因
    if (scoreTracking.reason && scoreTracking.reason.length > 0) {
        scoreTracking.reason.forEach(reason => {
            const reasonItem = document.createElement('li');
            reasonItem.textContent = reason;
            reasonsList.appendChild(reasonItem);
        });
    } else {
        const noReasonItem = document.createElement('li');
        noReasonItem.textContent = '无变化原因';
        reasonsList.appendChild(noReasonItem);
    }
    
    // 组装弹窗内容
    content.appendChild(originalScore);
    content.appendChild(currentScore);
    content.appendChild(scoreChange);
    content.appendChild(reasonsTitle);
    content.appendChild(reasonsList);
    
    popup.appendChild(closeButton);
    popup.appendChild(title);
    popup.appendChild(content);
    
    // 获取按钮位置并定位弹窗
    const buttonRect = buttonElement.getBoundingClientRect();
    const buttonTop = buttonRect.top;
    const buttonRight = buttonRect.right;
    
    // 计算弹窗位置，确保在屏幕内
    // 先设置默认位置
    popup.style.position = 'fixed'; // 改为fixed，这样不会受到滚动的影响
    
    // 判断右侧空间
    const rightSpace = window.innerWidth - buttonRight;
    if (rightSpace < 340) { // 如果右侧空间不足（320px宽度+20px边距）
        popup.style.right = 'auto';
        popup.style.left = (window.innerWidth - 340) + 'px'; // 靠右侧显示，但留出边距
    } else {
        popup.style.left = buttonRight + 'px';
    }
    
    // 判断底部空间
    const popupHeight = 250; // 预估高度
    if (buttonTop + popupHeight > window.innerHeight) {
        popup.style.top = (window.innerHeight - popupHeight - 10) + 'px';
    } else {
        popup.style.top = buttonTop + 'px';
    }
    
    // 添加到文档
    document.body.appendChild(popup);
    
    // 点击弹窗外部时关闭 - 已禁用，只允许通过关闭按钮关闭
    /*
    document.addEventListener('click', function closePopup(e) {
        if (!popup.contains(e.target) && e.target !== buttonElement) {
            popup.remove();
            document.removeEventListener('click', closePopup);
        }
    });
    */
} 

// 显示Actions弹窗
function showActionsPopup(actions, buttonElement) {
    // 确保actions是数组
    actions = Array.isArray(actions) ? actions : [];
    
    // 移除任何已存在的弹窗
    const existingPopup = document.querySelector('.actions-popup');
    if (existingPopup) {
        existingPopup.remove();
    }
    
    // 创建弹窗
    const popup = document.createElement('div');
    popup.className = 'actions-popup';
    
    // 获取按钮位置，以便正确定位弹窗
    const buttonRect = buttonElement.getBoundingClientRect();
    
    // 设置弹窗位置
    popup.style.position = 'absolute';
    popup.style.top = `${buttonRect.bottom + window.scrollY + 10}px`;
    popup.style.left = `${buttonRect.left + window.scrollX - 250 + buttonRect.width / 2}px`;
    popup.style.zIndex = '1000';
    
    // 弹窗内容
    let popupHTML = '<div class="actions-popup-header">Actions 列表<button class="actions-popup-close">&times;</button></div><div class="actions-popup-content">';
    
    if (actions.length === 0) {
        popupHTML += '<div class="actions-item">没有Actions数据</div>';
    } else {
        actions.forEach((action, index) => {
            popupHTML += `
                <div class="actions-item">
                    <div class="actions-item-header">Action ${index + 1}:</div>
                    <div class="actions-item-type"><strong>类型:</strong> ${action.action}</div>
                    <div class="actions-item-params"><strong>参数:</strong></div>
                    <pre>${JSON.stringify(action.params, null, 2)}</pre>
                </div>
            `;
        });
    }
    
    popupHTML += '</div>';
    popup.innerHTML = popupHTML;
    
    // 添加到body
    document.body.appendChild(popup);
    
    // 添加关闭按钮事件
    const closeButton = popup.querySelector('.actions-popup-close');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            popup.remove();
        });
    }
    
    // 点击外部关闭功能 - 已禁用，只允许通过关闭按钮关闭
    /*
    document.addEventListener('click', function closePopup(e) {
        if (!popup.contains(e.target) && e.target !== buttonElement) {
            popup.remove();
            document.removeEventListener('click', closePopup);
        }
    });
    */
    
    // 按下ESC键关闭
    document.addEventListener('keydown', function escClose(e) {
        if (e.key === 'Escape') {
            popup.remove();
            document.removeEventListener('keydown', escClose);
        }
    });
} 

// 加载保存的APIKey
function loadSavedApiKey() {
    const apiKeyInput = document.getElementById('apiKeyInput');
    if (apiKeyInput) {
        const savedApiKey = localStorage.getItem('interview_apikey');
        if (savedApiKey) {
            apiKeyInput.value = savedApiKey;
        }
    }
}

// 保存APIKey到localStorage
function saveApiKey() {
    const apiKeyInput = document.getElementById('apiKeyInput');
    if (apiKeyInput) {
        const apiKey = apiKeyInput.value.trim();
        if (apiKey) {
            localStorage.setItem('interview_apikey', apiKey);
        } else {
            localStorage.removeItem('interview_apikey');
        }
    }
} 