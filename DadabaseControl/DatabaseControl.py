import sqlite3
import json
from datetime import datetime, timedelta
import uuid
import hashlib
import random
import string
from DadabaseControl.DatabaseControl2 import get_job_classification
import sqlite3

DB_NAME = "sqlite3.db"

def _connect_db():
    """建立数据库连接"""
    try:
        conn = sqlite3.connect(DB_NAME)
        return conn
    except sqlite3.Error as e:
        print(f"数据库连接失败: {e}")
        return None

def migrate_database():
    """数据库迁移函数，用于添加新字段"""
    conn = _connect_db()
    if conn:
        cursor = conn.cursor()
        try:
            # 检查并添加 user_activation_command 字段到 tenant_global_settings 表
            cursor.execute("PRAGMA table_info(tenant_global_settings)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'user_activation_command' not in columns:
                cursor.execute("ALTER TABLE tenant_global_settings ADD COLUMN user_activation_command TEXT")
                print("已添加 user_activation_command 字段到 tenant_global_settings 表")

            conn.commit()
        except sqlite3.Error as e:
            print(f"数据库迁移失败: {e}")
        finally:
            conn.close()

def init_table():
    conn = _connect_db()
    if conn:
        cursor = conn.cursor()
        try:
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS position (
            dataId TEXT PRIMARY KEY,
            positionName TEXT,
            jobCity TEXT,
            age TEXT, -- 存储为 JSON 字符串 "[min, max]"
            gender TEXT,
            educationRequirements TEXT, -- 存储为 JSON 字符串 "[\"req1\", \"req2\"]"
            experienceRequirements TEXT, -- 存储为 JSON 字符串 "[\"req1\", \"req2\"]"
            salaryRange TEXT,
            jobIntentions TEXT, -- 存储为 JSON 字符串
            excludedKeywordsAnd TEXT, -- 存储为 JSON 字符串
            keywordsMultiple TEXT, -- 存储为 JSON 字符串 "[[\"k1\",\"k2\"],[\"k3\"]]"
            activityLevel TEXT, -- 存储为 JSON 字符串
            expectedCities TEXT, -- 存储为 JSON 字符串
            expectPositions TEXT, -- 存储为 JSON 字符串
            preferredCompanies TEXT, -- 存储为 JSON 字符串
            excludedCompanies TEXT, -- 存储为 JSON 字符串
            preferredSchools TEXT, -- 存储为 JSON 字符串
            excludedSchools TEXT, -- 存储为 JSON 字符串
            recentViewed TEXT,
            exchangeResumeWithColleague TEXT,
            schoolType TEXT, -- 存储为 JSON 字符串
            onlyFirstSchool INTEGER, -- 布尔值存储为 0 或 1
            jobHoppingFrequency TEXT,
            seekerKeywords TEXT, -- 存储为 JSON 字符串
            createdAt TEXT,
            updatedAt TEXT,
            accountId INTEGER,
            aiFilterRequirements TEXT,
            matchRate REAL,
            job_classification_id TEXT,
            boss_account TEXT,
            virtual_hr_id, TEXT,
            boss_scene TEXT,
            enable_boss_scene INTEGER,
            device_info TEXT,
            tenant_id TEXT
        );
        """)
            
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS user (
            userid TEXT PRIMARY KEY,
            positionId TEXT,
            gender TEXT,
            age INTEGER,
            real_name TEXT,
            name TEXT,
            phone TEXT,
            resume TEXT,
            resume_text TEXT,
            wxid TEXT,
            score INTEGER,
            sceneId TEXT,
            city TEXT,
            address TEXT,
            remarks TEXT,
            wechat_nickname TEXT,
            source TEXT,
            title TEXT,
            education_experience TEXT,
            graduate_school TEXT,
            virtual_hr_id TEXT,
            tenant_id TEXT,
            concerns TEXT,
            createdAt TEXT
        );
        """)
            
            # 添加批量管理表
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS batch_management (
            id TEXT PRIMARY KEY,
            city TEXT,
            job_category TEXT,
            work_content TEXT,
            recruitment_requirements TEXT,
            work_time TEXT,
            salary_benefits TEXT,
            interview_time TEXT,
            training_time TEXT,
            recruitment_area TEXT,
            interview_address TEXT,
            remarks TEXT,
            tenant_id TEXT,
            createdAt TEXT,
            updatedAt TEXT
        );
        """)

            # 添加管理员用户表
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS admin_user07082 (
            id TEXT PRIMARY KEY,
            account TEXT UNIQUE,
            password TEXT,
            salt TEXT,
            nickname TEXT,
            createdAt TEXT
        );
        """)
            
            # 添加租户用户表
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS tenant07082 (
            id TEXT PRIMARY KEY,
            account TEXT UNIQUE,
            password TEXT,
            salt TEXT,
            nickname TEXT,
            phone TEXT,
            email TEXT,
            wechat TEXT,
            createdAt TEXT
        );
        """)
            
            # 添加租户全局配置
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS tenant_global_settings (
            id TEXT PRIMARY KEY,
            tenant_id TEXT,
            user_title TEXT,
            user_activation_command TEXT,
            createdAt TEXT
        );
        """)
            
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS chatlog (
            id TEXT PRIMARY KEY,
            userid TEXT,
            positionId TEXT,
            role TEXT,
            content TEXT,
            sceneId TEXT,
            createdAt TEXT,
            score INTEGER,
            score_tracking TEXT,
            
            actions TEXT
        );
        """)
            
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS scene (
            sceneId TEXT PRIMARY KEY,
            job_classification_id TEXT,
            sceneName TEXT,
            sceneDescription TEXT,
            scoreRange TEXT,
            createdAt TEXT,
            isDefault INTEGER,
            score_trigger_id TEXT,
            enable_semantic_classifier INTEGER,
            enable_answer_classifier INTEGER,
            enable_score_trigger INTEGER,
            enable_semantic_trigger INTEGER,
            allowed_to_fall_back INTEGER,
            semantic_classifier_ids TEXT,
            score_trigger_ids TEXT,
            semantic_trigger_ids TEXT,
            answer_classifier_id TEXT
        );
        """)
            
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS scene_question (
            questionId TEXT PRIMARY KEY,
            sceneId TEXT,
            questionContent TEXT,
            questionScore INTEGER,
            createdAt TEXT,
            seq INTEGER
        );
        """)

            # 检查scene_switcher表是否需要更新（移除switch_type字段）
            cursor.execute("PRAGMA table_info(scene_switcher)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'switch_type' in columns:
                # 需要重建表以移除switch_type字段
                print("正在更新scene_switcher表结构...")

                # 备份现有数据
                cursor.execute("SELECT * FROM scene_switcher")
                existing_data = cursor.fetchall()

                # 删除旧表
                cursor.execute("DROP TABLE scene_switcher")

                # 创建新表
                cursor.execute("""
            CREATE TABLE scene_switcher (
                switcher_id TEXT PRIMARY KEY,
                scene_id TEXT NOT NULL,
                scene_name TEXT NOT NULL,
                score_condition TEXT,
                semantic_condition TEXT,
                target_config TEXT NOT NULL,
                created_at TEXT NOT NULL,
                tenant_id TEXT NOT NULL,
                FOREIGN KEY (scene_id) REFERENCES scene(sceneId)
            );
            """)

                # 恢复数据（跳过switch_type字段）
                for row in existing_data:
                    cursor.execute("""
                INSERT INTO scene_switcher (switcher_id, scene_id, scene_name, score_condition,
                                          semantic_condition, target_config, created_at, tenant_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (row[0], row[1], row[2], row[4], row[5], row[6], row[7], row[8]))

                print("scene_switcher表结构更新完成")
            else:
                # 表不存在或已经是新结构，直接创建
                cursor.execute("""
            CREATE TABLE IF NOT EXISTS scene_switcher (
                switcher_id TEXT PRIMARY KEY,
                scene_id TEXT NOT NULL,
                scene_name TEXT NOT NULL,
                score_condition TEXT,
                semantic_condition TEXT,
                target_config TEXT NOT NULL,
                created_at TEXT NOT NULL,
                tenant_id TEXT NOT NULL,
                FOREIGN KEY (scene_id) REFERENCES scene(sceneId)
            );
            """)

            # 添加流程图位置信息表
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS flowchart_positions (
            id TEXT PRIMARY KEY,
            job_classification_id TEXT NOT NULL,
            element_type TEXT NOT NULL,
            element_id TEXT NOT NULL,
            position_data TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            tenant_id TEXT NOT NULL
        );
        """)
            
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS score (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            userid TEXT,
            positionId TEXT,
            sceneId TEXT,
            questionId TEXT,
            questionContent TEXT,
            questionScore INTEGER,
            score INTEGER,
            createdAt TEXT
        );
        """)
            
            cursor.execute("""
        CREATE TABLE IF NOT EXISTS job_classification (
            id TEXT PRIMARY KEY,
            class_name TEXT,
            class_description TEXT,
            virtual_hr_id TEXT,
            createdAt TEXT
        );
        """)

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS score_trigger (
                    id TEXT PRIMARY KEY,
                    score_trigger_name TEXT,
                    score INTEGER,
                    action TEXT,
                    repeated_triggering INTEGER,
                    explanation TEXT,
                    createdAt TEXT
                );
            """)

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS score_trigger_log (
                    id TEXT PRIMARY KEY,
                    userid TEXT,
                    sceneId TEXT,
                    score_trigger_id TEXT,
                    score_trigger_name TEXT,
                    createdAt TEXT
                );
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS virtual_hr (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    prompt TEXT,
                    tenant_id TEXT,
                    createdAt TEXT
                );
            """)
            
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS semantic_trigger (
                    id TEXT PRIMARY KEY,
                    semantic_trigger_name TEXT,
                    semantic_content TEXT,
                    action TEXT,
                    repeated_triggering INTEGER,
                    explanation TEXT,
                    createdAt TEXT
                );
            """)
            
            # 添加语义分类器表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS semantic_classifier (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    description TEXT,
                    createdAt TEXT
                );
            """)
            
            # 添加语义分类器问题表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS semantic_classifier_question (
                    id TEXT PRIMARY KEY,
                    classifier_id TEXT,
                    content TEXT,
                    score REAL,
                    createdAt TEXT,
                    FOREIGN KEY (classifier_id) REFERENCES semantic_classifier(id)
                );
            """)
            
            # 添加答案分类器表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS answer_classifier (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    description TEXT,
                    active_multiplier REAL,
                    neutral_multiplier REAL,
                    negative_multiplier REAL,
                    type TEXT, -- 旧字段，保留向后兼容
                    multiplier REAL, -- 旧字段，保留向后兼容
                    createdAt TEXT
                );
            """)
            
            # 添加tenant_apikey表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS tenant_apikey (
                    id TEXT PRIMARY KEY,
                    tenant_id TEXT,
                    name TEXT,
                    apikey TEXT UNIQUE,
                    createdAt TEXT,
                    FOREIGN KEY (tenant_id) REFERENCES tenant07082(id)
                );
            """)
            
            # 添加apikey_token表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS apikey_token (
                    id TEXT PRIMARY KEY,
                    tenant_id TEXT,
                    apikey TEXT,
                    uptoken INTEGER,
                    downtoken INTEGER,
                    createdAt TEXT,
                    FOREIGN KEY (tenant_id) REFERENCES tenant07082(id),
                    FOREIGN KEY (apikey) REFERENCES tenant_apikey(apikey)
                );
            """)
            
            # 添加渠道接入表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS channel (
                    id TEXT PRIMARY KEY,
                    tenant_id TEXT,
                    apikey TEXT,
                    channel_type TEXT,
                    enable_pre_scenario INTEGER DEFAULT 0,
                    pre_scenario_prompt TEXT,
                    createdAt TEXT,
                    FOREIGN KEY (tenant_id) REFERENCES tenant07082(id)
                );
            """)
            
            conn.commit()
            print("所有表已创建或已存在。")

            # 初始化定时分析相关表
            from DadabaseControl.DatabaseControl4 import init_analysis_tables
            init_analysis_tables()

        except sqlite3.Error as e:
            print(f"创建表失败: {e}")
        finally:
            conn.close()

# Flowchart Positions表操作
def save_flowchart_position(job_classification_id: str, element_type: str, element_id: str, position_data: dict, tenant_id: str):
    """
    保存流程图元素位置信息
    """
    conn = _connect_db()
    if not conn:
        return False

    position_id = f"{job_classification_id}_{element_type}_{element_id}"
    now = datetime.now().isoformat()

    # 序列化位置数据
    position_json = json.dumps(position_data, ensure_ascii=False)

    # 先尝试更新，如果不存在则插入
    try:
        cursor = conn.cursor()

        # 检查是否已存在
        cursor.execute("SELECT id FROM flowchart_positions WHERE id = ?", (position_id,))
        exists = cursor.fetchone()

        if exists:
            # 更新
            update_sql = """
            UPDATE flowchart_positions
            SET position_data = ?, updated_at = ?
            WHERE id = ?
            """
            cursor.execute(update_sql, (position_json, now, position_id))
        else:
            # 插入
            insert_sql = """
            INSERT INTO flowchart_positions (id, job_classification_id, element_type, element_id,
                                           position_data, created_at, updated_at, tenant_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            cursor.execute(insert_sql, (position_id, job_classification_id, element_type, element_id,
                                      position_json, now, now, tenant_id))

        conn.commit()
        return True
    except sqlite3.Error as e:
        print(f"保存流程图位置失败: {e}")
        return False
    finally:
        conn.close()

def get_flowchart_positions(job_classification_id: str):
    """
    获取职位分类下的所有流程图位置信息
    """
    conn = _connect_db()
    if not conn:
        return {}

    select_sql = "SELECT * FROM flowchart_positions WHERE job_classification_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (job_classification_id,))
        rows = cursor.fetchall()

        positions = {}
        if rows:
            col_names = [description[0] for description in cursor.description]
            for row in rows:
                position_data = dict(zip(col_names, row))
                # 反序列化位置数据
                try:
                    position_data['position_data'] = json.loads(position_data['position_data'])
                except json.JSONDecodeError:
                    pass

                key = f"{position_data['element_type']}_{position_data['element_id']}"
                positions[key] = position_data['position_data']

        return positions
    except sqlite3.Error as e:
        print(f"获取流程图位置失败: {e}")
        return {}
    finally:
        conn.close()

# Scene Switcher表操作
def insert_scene_switcher(data: dict):
    """
    插入一条新的场景切换器记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["created_at"] = data.get("created_at", datetime.now().isoformat())
    data["switcher_id"] = str(uuid.uuid4()).replace("-", "")

    # 序列化JSON字段
    if "score_condition" in data and isinstance(data["score_condition"], dict):
        data["score_condition"] = json.dumps(data["score_condition"], ensure_ascii=False)
    if "target_config" in data and isinstance(data["target_config"], dict):
        data["target_config"] = json.dumps(data["target_config"], ensure_ascii=False)

    insert_sql = """
    INSERT INTO scene_switcher (switcher_id, scene_id, scene_name,
                               score_condition, semantic_condition, target_config,
                               created_at, tenant_id)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    """

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, (
            data["switcher_id"], data["scene_id"], data["scene_name"],
            data.get("score_condition"), data.get("semantic_condition"),
            data["target_config"], data["created_at"], data["tenant_id"]
        ))
        conn.commit()
        return data["switcher_id"]
    except sqlite3.Error as e:
        print(f"插入场景切换器失败: {e}")
        return False
    finally:
        conn.close()

def get_scene_switcher(switcher_id: str):
    """
    根据 switcher_id 查询场景切换器记录。
    """
    conn = _connect_db()
    if not conn:
        return None

    select_sql = "SELECT * FROM scene_switcher WHERE switcher_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (switcher_id,))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            switcher_data = dict(zip(col_names, row))
            # 反序列化JSON字段
            for key in ["score_condition", "target_config"]:
                if switcher_data.get(key):
                    try:
                        switcher_data[key] = json.loads(switcher_data[key])
                    except json.JSONDecodeError:
                        pass
            return switcher_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询场景切换器失败: {e}")
        return None
    finally:
        conn.close()

def get_scene_switchers_by_job_classification(job_classification_id: str):
    """
    根据职位分类ID查询所有相关的场景切换器。
    """
    conn = _connect_db()
    if not conn:
        return []

    # 先获取该职位分类下的所有场景ID
    from DadabaseControl.DatabaseControl5 import get_scenes_by_job_classification
    scenes = get_scenes_by_job_classification(job_classification_id)
    if not scenes:
        return []

    scene_ids = [scene["sceneId"] for scene in scenes]
    placeholders = ",".join(["?" for _ in scene_ids])

    select_sql = f"SELECT * FROM scene_switcher WHERE scene_id IN ({placeholders})"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, scene_ids)
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            switchers = []
            for row in rows:
                switcher_data = dict(zip(col_names, row))
                # 反序列化JSON字段
                for key in ["score_condition", "target_config"]:
                    if switcher_data.get(key):
                        try:
                            switcher_data[key] = json.loads(switcher_data[key])
                        except json.JSONDecodeError:
                            pass
                switchers.append(switcher_data)
            return switchers
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询场景切换器列表失败: {e}")
        return []
    finally:
        conn.close()

def get_scene_switchers(scene_id: str):
    """
    根据 scene_id 查询所有相关的场景切换器。
    """
    conn = _connect_db()
    if not conn:
        return []
    
    select_sql = "SELECT * FROM scene_switcher WHERE scene_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (scene_id,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            switchers = []
            for row in rows:
                switcher_data = dict(zip(col_names, row))
                # 反序列化JSON字段
                for key in ["score_condition", "target_config"]:
                    if switcher_data.get(key):
                        try:
                            switcher_data[key] = json.loads(switcher_data[key])
                        except json.JSONDecodeError:
                            pass
                switchers.append(switcher_data)
            return switchers
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询场景切换器列表失败: {e}")
        return []
    finally:
        conn.close()

def update_scene_switcher(switcher_id: str, data: dict):
    """
    更新场景切换器记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    # 序列化JSON字段
    if "score_condition" in data and isinstance(data["score_condition"], dict):
        data["score_condition"] = json.dumps(data["score_condition"], ensure_ascii=False)
    if "target_config" in data and isinstance(data["target_config"], dict):
        data["target_config"] = json.dumps(data["target_config"], ensure_ascii=False)

    # 构建更新SQL
    set_clauses = []
    values = []
    for key, value in data.items():
        if key != "switcher_id":  # 不更新主键
            set_clauses.append(f"{key} = ?")
            values.append(value)

    if not set_clauses:
        return False

    values.append(switcher_id)
    update_sql = f"UPDATE scene_switcher SET {', '.join(set_clauses)} WHERE switcher_id = ?"

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"更新场景切换器失败: {e}")
        return False
    finally:
        conn.close()



def delete_scene_switcher(switcher_id: str):
    """
    删除场景切换器记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM scene_switcher WHERE switcher_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (switcher_id,))
        conn.commit()
        return cursor.rowcount > 0
    except sqlite3.Error as e:
        print(f"删除场景切换器失败: {e}")
        return False
    finally:
        conn.close()


def update_scene_switchers_target_scene_name(job_classification_id: str, old_scene_name: str, new_scene_name: str):
    """
    更新指定职位分类下所有指向特定场景的切换器的目标场景名称。
    当场景名称被修改时，需要同步更新所有指向该场景的切换器。
    """
    conn = _connect_db()
    if not conn:
        return False

    try:
        # 首先获取该职位分类下的所有场景ID
        from DadabaseControl.DatabaseControl5 import get_scenes_by_job_classification
        scenes = get_scenes_by_job_classification(job_classification_id)
        if not scenes:
            return True  # 没有场景，直接返回成功

        scene_ids = [scene["sceneId"] for scene in scenes]
        placeholders = ",".join(["?" for _ in scene_ids])

        # 查询该职位分类下的所有切换器
        select_sql = f"SELECT * FROM scene_switcher WHERE scene_id IN ({placeholders})"
        cursor = conn.cursor()
        cursor.execute(select_sql, scene_ids)
        switchers = cursor.fetchall()

        if not switchers:
            return True  # 没有切换器，直接返回成功

        # 获取列名
        col_names = [description[0] for description in cursor.description]

        # 遍历所有切换器，检查并更新目标场景名称
        updated_count = 0
        for row in switchers:
            switcher_data = dict(zip(col_names, row))

            # 解析target_config
            target_config = switcher_data.get("target_config")
            if target_config:
                try:
                    target_config_dict = json.loads(target_config)

                    # 检查是否指向需要更新的场景
                    if target_config_dict.get("dist_scene") == old_scene_name:
                        # 更新目标场景名称
                        target_config_dict["dist_scene"] = new_scene_name

                        # 更新数据库记录
                        update_sql = "UPDATE scene_switcher SET target_config = ? WHERE switcher_id = ?"
                        cursor.execute(update_sql, (
                            json.dumps(target_config_dict, ensure_ascii=False),
                            switcher_data["switcher_id"]
                        ))
                        updated_count += 1

                except json.JSONDecodeError:
                    # 如果JSON解析失败，跳过这个切换器
                    print(f"切换器 {switcher_data['switcher_id']} 的target_config JSON格式错误")
                    continue

        conn.commit()
        print(f"成功更新了 {updated_count} 个切换器的目标场景名称：{old_scene_name} -> {new_scene_name}")
        return True

    except sqlite3.Error as e:
        print(f"更新切换器目标场景名称失败: {e}")
        return False
    finally:
        conn.close()

def _serialize_value(value):
    """将列表或字典序列化为 JSON 字符串，否则直接返回"""
    if isinstance(value, (list, dict)):
        return json.dumps(value, ensure_ascii=False)
    return value

def _deserialize_value(value):
    """尝试将字符串反序列化为 JSON，如果失败则返回原字符串"""
    if isinstance(value, str):
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            pass
    return value

def _serialize_id_list(id_list):
    """
    将ID列表序列化为JSON字符串
    """
    if not id_list:
        return None
    
    # 确保所有值都是字符串
    id_list = [str(id) for id in id_list if id]
    
    # 如果列表为空，返回None
    if not id_list:
        return None
        
    # 序列化为JSON字符串
    return json.dumps(id_list)

def _deserialize_id_list(json_str):
    """
    将JSON字符串反序列化为ID列表
    """
    if not json_str:
        return []
    
    try:
        id_list = json.loads(json_str)
        # 确保结果是列表
        if not isinstance(id_list, list):
            return []
        return id_list
    except:
        return []

def insert_position(data: dict):
    """
    插入一条新的职位记录。
    """

    conn = _connect_db()
    if not conn:
        return False,f"数据库连接失败"

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    data["updatedAt"] = data.get("updatedAt", datetime.now().isoformat())
    data["dataId"] = str(uuid.uuid4()).replace("-", "")
    data["job_classification_id"] = data.get("job_classification_id", "")
    tenant_id = data.get("tenant_id", "")
    data["virtual_hr_id"] = get_job_classification(data["job_classification_id"],"")["virtual_hr_id"]

    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT OR IGNORE INTO position ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        print(f"职位 '{data.get('positionName', '未知')}' (dataId: {data.get('dataId', '未知')}) 插入成功。")
        return True,data["dataId"]
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return False,str(e)
    finally:
        conn.close()

def get_position(dataId: str,tenant_id: str):
    """
    根据 dataId 查询职位记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None

    if tenant_id == "":
        select_sql = "SELECT * FROM position WHERE dataId = ?"
    else:
        select_sql = "SELECT * FROM position WHERE dataId = ? AND tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_sql, (dataId,))
        else:
            cursor.execute(select_sql, (dataId,tenant_id))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            position_data = dict(zip(col_names, row))
            for key, value in position_data.items():
                position_data[key] = _deserialize_value(value)
            return position_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_all_positions(tenant_id: str):
    """
    查询所有职位记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []
    if tenant_id == "":
        select_all_sql = "SELECT * FROM position"
    else:
        select_all_sql = "SELECT * FROM position WHERE tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_all_sql)
        else:
            cursor.execute(select_all_sql, (tenant_id,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_positions = []
            for row in rows:
                position_data = dict(zip(col_names, row))
                for key, value in position_data.items():
                    position_data[key] = _deserialize_value(value)
                all_positions.append(position_data)
            return all_positions
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询所有数据失败: {e}")
        return []
    finally:
        conn.close()

def get_positions_by_job_classification(job_classification_id: str):
    """
    根据 job_classification_id 查询职位记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []
    
    select_sql = "SELECT * FROM position WHERE job_classification_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (job_classification_id,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            positions = []
            for row in rows:
                position_data = dict(zip(col_names, row))
                for key, value in position_data.items():
                    position_data[key] = _deserialize_value(value)
                positions.append(position_data)
            return positions
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []


def update_position(dataId: str, updates: dict):
    """
    根据 dataId 更新职位记录。
    updates 字典包含要更新的字段及其新值。
    """
    conn = _connect_db()
    if not conn:
        return False

    updates["updatedAt"] = datetime.now().isoformat()

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items()}

    set_clauses = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE position SET {set_clauses} WHERE dataId = ?"

    values = list(serialized_updates.values())
    values.append(dataId)

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"职位 dataId: {dataId} 更新成功。")
            return True
        else:
            print(f"未找到 dataId 为 {dataId} 的职位，未能更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_position(dataId: str,tenant_id: str):
    """
    根据 dataId 删除职位记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM position WHERE dataId = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (dataId,))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"职位 dataId: {dataId} 删除成功。")
            return True
        else:
            print(f"未找到 dataId 为 {dataId} 的职位，未能删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

# User表操作
def insert_user(data: dict):
    """
    插入一条新的用户记录。
    如果 userid 已存在，则不插入。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    data["userid"] = str(uuid.uuid4()).replace("-", "")
    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT OR IGNORE INTO user ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"用户 '{data.get('name', '未知')}' (userid: {data.get('userid', '未知')}) 插入成功。")
            return data["userid"]
        else:
            print(f"用户 '{data.get('name', '未知')}' (userid: {data.get('userid', '未知')}) 已存在，未插入。")
            return None
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return data["userid"]
    finally:
        conn.close()

def get_user(userid: str,tenant_id: str):
    """
    根据 userid 查询用户记录。
    返回一个字典，如果未找到则返回 None。
    """
    conn = _connect_db()
    if not conn:
        return None

    if tenant_id == "":
        select_sql = "SELECT * FROM user WHERE userid = ?"
    else:
        select_sql = "SELECT * FROM user WHERE userid = ? AND tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_sql, (userid,))
        else:
            cursor.execute(select_sql, (userid,tenant_id))
        row = cursor.fetchone()
        if row:
            col_names = [description[0] for description in cursor.description]
            user_data = dict(zip(col_names, row))
            for key, value in user_data.items():
                user_data[key] = _deserialize_value(value)
            return user_data
        else:
            return None
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return None
    finally:
        conn.close()

def get_all_users(tenant_id: str):
    """
    查询所有用户记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    if tenant_id == "":
        select_all_sql = "SELECT * FROM user"
    else:
        select_all_sql = "SELECT * FROM user WHERE tenant_id = ?"
    try:
        cursor = conn.cursor()
        if tenant_id == "":
            cursor.execute(select_all_sql)
        else:
            cursor.execute(select_all_sql, (tenant_id,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_users = []
            for row in rows:
                user_data = dict(zip(col_names, row))
                for key, value in user_data.items():
                    user_data[key] = _deserialize_value(value)
                all_users.append(user_data)
            return all_users
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询所有数据失败: {e}")
        return []
    finally:
        conn.close()

def get_users_by_position(position_id: str, tenant_id: str = ""):
    """
    根据职位ID查询用户记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    if tenant_id == "":
        select_sql = "SELECT * FROM user WHERE positionId = ?"
        params = (position_id,)
    else:
        select_sql = "SELECT * FROM user WHERE positionId = ? AND tenant_id = ?"
        params = (position_id, tenant_id)

    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, params)
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            users = []
            for row in rows:
                user_data = dict(zip(col_names, row))
                for key, value in user_data.items():
                    user_data[key] = _deserialize_value(value)
                users.append(user_data)
            return users
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询用户数据失败: {e}")
        return []
    finally:
        conn.close()

def update_user(userid: str, updates: dict,tenant_id: str):
    """
    根据 userid 更新用户记录。
    updates 字典包含要更新的字段及其新值，如果某个键的值为空，则不更新，如果某个键的值为None，则不更新，如果没有找到键，则不更新。
    """
    conn = _connect_db()
    if not conn:
        return False

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items() if v is not None and v != "" and k in updates}

    set_clauses = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE user SET {set_clauses} WHERE userid = ? AND tenant_id = ?"

    values = list(serialized_updates.values())
    values.append(userid)
    values.append(tenant_id)
    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"用户 userid: {userid} 更新成功。")
            return True
        else:
            print(f"未找到 userid 为 {userid} 的用户，未能更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def delete_user(userid: str,tenant_id: str):
    """
    根据 userid 删除用户记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM user WHERE userid = ? AND tenant_id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (userid,tenant_id))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"用户 userid: {userid} 删除成功。")
            return True
        else:
            print(f"未找到 userid 为 {userid} 的用户，未能删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

# Chatlog表操作
def insert_chatlog(data: dict):
    """
    插入一条新的聊天记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    data["createdAt"] = data.get("createdAt", datetime.now().isoformat())
    data["id"] = str(uuid.uuid4()).replace("-", "")
    serialized_data = {k: _serialize_value(v) for k, v in data.items()}

    columns = ', '.join(serialized_data.keys())
    placeholders = ', '.join(['?' for _ in serialized_data.keys()])
    insert_sql = f"INSERT INTO chatlog ({columns}) VALUES ({placeholders})"

    try:
        cursor = conn.cursor()
        cursor.execute(insert_sql, list(serialized_data.values()))
        conn.commit()
        print(f"聊天记录插入成功。")
        return True
    except sqlite3.Error as e:
        print(f"插入数据失败: {e}")
        return False
    finally:
        conn.close()

def get_chatlogs_by_user(userid: str):
    """
    根据 userid 查询聊天记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM chatlog WHERE userid = ? ORDER BY createdAt"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (userid,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_chatlogs = []
            for row in rows:
                chatlog_data = dict(zip(col_names, row))
                for key, value in chatlog_data.items():
                    chatlog_data[key] = _deserialize_value(value)
                all_chatlogs.append(chatlog_data)
            return all_chatlogs
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def update_chatlog(id: str, updates: dict):
    """
    根据 id 更新聊天记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    serialized_updates = {k: _serialize_value(v) for k, v in updates.items()}

    set_clauses = ', '.join([f"{key} = ?" for key in serialized_updates.keys()])
    update_sql = f"UPDATE chatlog SET {set_clauses} WHERE id = ?"

    values = list(serialized_updates.values())
    values.append(id)

    try:
        cursor = conn.cursor()
        cursor.execute(update_sql, values)
        conn.commit()
        if cursor.rowcount > 0:
            print(f"聊天记录 id: {id} 更新成功。")
            return True
        else:
            print(f"未找到 id 为 {id} 的聊天记录，未能更新。")
            return False
    except sqlite3.Error as e:
        print(f"更新数据失败: {e}")
        return False
    finally:
        conn.close()

def get_chatlogs_by_userid_timestamp(userid: str,timestamp: int):
    conn = _connect_db()
    if not conn:
        return []
    select_sql = "SELECT * FROM chatlog WHERE userid = ? AND createdAt = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (userid,timestamp))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_chatlogs = []
            for row in rows:
                chatlog_data = dict(zip(col_names, row))
                for key, value in chatlog_data.items():
                    chatlog_data[key] = _deserialize_value(value)
                all_chatlogs.append(chatlog_data)
            return all_chatlogs
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def delete_chatlogs_by_user(userid: str):
    conn = _connect_db()
    if not conn:
        return False
    delete_sql = "DELETE FROM chatlog WHERE userid = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (userid,))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"聊天记录 userid: {userid} 删除成功。")
            return True
        else:
            print(f"未找到 userid 为 {userid} 的聊天记录，未能删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

def get_chatlogs_by_scene(sceneId: int):
    """
    根据 sceneId 查询聊天记录。
    返回一个包含字典的列表。
    """
    conn = _connect_db()
    if not conn:
        return []

    select_sql = "SELECT * FROM chatlog WHERE sceneId = ? ORDER BY createdAt"
    try:
        cursor = conn.cursor()
        cursor.execute(select_sql, (sceneId,))
        rows = cursor.fetchall()
        if rows:
            col_names = [description[0] for description in cursor.description]
            all_chatlogs = []
            for row in rows:
                chatlog_data = dict(zip(col_names, row))
                for key, value in chatlog_data.items():
                    chatlog_data[key] = _deserialize_value(value)
                all_chatlogs.append(chatlog_data)
            return all_chatlogs
        else:
            return []
    except sqlite3.Error as e:
        print(f"查询数据失败: {e}")
        return []
    finally:
        conn.close()

def delete_chatlog(id: int):
    """
    根据 id 删除聊天记录。
    """
    conn = _connect_db()
    if not conn:
        return False

    delete_sql = "DELETE FROM chatlog WHERE id = ?"
    try:
        cursor = conn.cursor()
        cursor.execute(delete_sql, (id,))
        conn.commit()
        if cursor.rowcount > 0:
            print(f"聊天记录 id: {id} 删除成功。")
            return True
        else:
            print(f"未找到 id 为 {id} 的聊天记录，未能删除。")
            return False
    except sqlite3.Error as e:
        print(f"删除数据失败: {e}")
        return False
    finally:
        conn.close()

